package adhoc.mock

import org.web3j.abi.EventEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.protocol.core.methods.response.EthLog
import org.web3j.protocol.core.methods.response.Log
import org.web3j.protocol.websocket.events.NewHead
import org.web3j.protocol.websocket.events.NewHeadsNotification
import org.web3j.protocol.websocket.events.NotificationParams

/**
 * Factory class for creating mock event objects used in testing.
 * This class centralizes the creation of various mock objects related to blockchain events,
 * improving maintainability and reusability across test classes.
 *
 * Note: This class provides data and configuration for mocks, but the actual Mock() creation
 * must be done within Spock test classes.
 */
class EventMockFactory {


	/**
	 * Create a mock AddProviderRole event definition for testing
	 * Event: AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId)
	 * @return Event definition for AddProviderRole
	 */
	static Event createMockAddProviderRoleEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// providerEoa (non-indexed)
			new TypeReference<Address>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddProviderRole", parameters)
	}

	/**
	 * Create a mock AddTokenByProvider event definition for testing
	 * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
	 * @return Event definition for AddTokenByProvider
	 */
	static Event createMockAddTokenByProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// tokenId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddTokenByProvider", parameters)
	}

	/**
	 * Create a mock AccessCtrl event definition for testing
	 * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
	 * @return Event definition for AddTokenByProvider
	 */
	static Event createAccessCtrlEvent() {
		def parameters = [
				// providerId (indexed)
				new TypeReference<Bytes32>(true) {},
				// tokenId (non-indexed)
				new TypeReference<Bytes32>(true) {},
				// traceId (non-indexed)
				new TypeReference<Bytes32>(true) {}
		]
		return new Event("AccessCtrl", parameters)
	}

	/**
	 * Create a proper AddProviderRole log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddProviderRole event data
	 */
	static Log createAddProviderRoleLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddProviderRole event signature using Web3j
		def addProviderRoleEvent = createMockAddProviderRoleEvent()
		def eventSignature = EventEncoder.encode(addProviderRoleEvent)
		println("AddProviderRole event signature: ${eventSignature}")

		// providerId (indexed parameter)
		def providerId = "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

		log.topics = [eventSignature, providerId]

		// Data contains: address providerEoa + bytes32 traceId
		// providerEoa: 0xa1b2c3d4e5f6789012345678901234567890abcd (20 bytes, padded to 32)
		// traceId: 0xef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef (32 bytes)
		log.data = "0x000000000000000000000000a1b2c3d4e5f6789012345678901234567890abcd" +
				"ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"

		log.transactionHash = "0xabc123"
		log.logIndex = BigInteger.valueOf(0)
		log.blockNumber = BigInteger.valueOf(1000)

		return log
	}

	/**
	 * Create a proper AddTokenByProvider log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddTokenByProvider event data
	 */
	static Log createAddTokenByProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddTokenByProvider event signature using Web3j
		def addTokenByProviderEvent = createMockAddTokenByProviderEvent()
		def eventSignature = EventEncoder.encode(addTokenByProviderEvent)
		println("AddTokenByProvider event signature: ${eventSignature}")

		// providerId (indexed parameter)
		def providerId = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [eventSignature, providerId]

		// Data contains: bytes32 tokenId + bytes32 traceId
		// tokenId: 0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba
		// traceId: 0x1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff
		log.data = "0x9876543210fedcba9876543210fedcba9876543210fedcba9876543210fedcba" +
				"1111222233334444555566667777888899990000aaaabbbbccccddddeeeeffff"

		log.transactionHash = "0xdef456"
		log.logIndex = BigInteger.valueOf(1)
		log.blockNumber = BigInteger.valueOf(1000)

		return log
	}

	/**
	 * Create a proper RoleAdminChanged log for
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleAdminChanged event data
	 */
	static Log createRoleAdminChangedLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate AddTokenByProvider event signature using Web3j
		def event = createAccessCtrlEvent()
		def eventSignature = EventEncoder.encode(event)
		println("AccessCtrl event signature: ${eventSignature}")

		def role = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def previousAdminRole = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"
		def newAdminRole = "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

		log.topics = [eventSignature, role, previousAdminRole, newAdminRole]

		log.transactionHash = "0xdef456"
		log.logIndex = BigInteger.valueOf(1)
		log.blockNumber = BigInteger.valueOf(1000)

		return log
	}
}
